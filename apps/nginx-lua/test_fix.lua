#!/usr/bin/env lua

-- 简单测试脚本来验证修复
local function test_sanitize_control_chars()
    -- 模拟 ngx.re.gsub 函数
    local ngx = {
        re = {
            gsub = function(str, pattern, replacement, options)
                -- 简单的模拟实现
                if pattern == "\\r" then
                    return str:gsub("\r", replacement), 1
                elseif pattern == "\\n" then
                    return str:gsub("\n", replacement), 1
                elseif pattern == "\\0" then
                    return str:gsub("\0", replacement), 1
                end
                return str, 0
            end
        }
    }
    
    -- 复制修复后的函数
    local function sanitize_control_chars(str)
        if not str then return "" end

        -- 先创建本地变量避免nil赋值
        local result = str
        local new_str
        local _ -- 声明局部变量 _ 避免全局变量竞态条件警告

        -- 安全替换，处理可能的nil返回
        new_str, _ = ngx.re.gsub(result, "\\r", "%0D", "jo")
        if new_str then result = new_str end

        new_str, _ = ngx.re.gsub(result, "\\n", "%0A", "jo")
        if new_str then result = new_str end

        new_str, _ = ngx.re.gsub(result, "\\0", "%00", "jo")
        if new_str then result = new_str end

        return result
    end
    
    -- 测试用例
    local test_cases = {
        "普通文本",
        "包含中文的URL参数：是",
        "test\rcarriage\nreturn\0null",
        "",
        nil
    }
    
    print("测试 sanitize_control_chars 函数:")
    for i, test_case in ipairs(test_cases) do
        local result = sanitize_control_chars(test_case)
        print(string.format("测试 %d: 输入='%s', 输出='%s'", 
            i, tostring(test_case), tostring(result)))
    end
    
    print("✅ 所有测试完成，没有全局变量警告")
end

test_sanitize_control_chars()
