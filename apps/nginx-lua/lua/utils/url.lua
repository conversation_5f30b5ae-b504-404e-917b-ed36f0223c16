local base64 = require "utils.base64"
local net_url = require "lib.net_url" -- 使用 net_url 库替代 resty_url
local logger = require "utils.logger"
local cjson = require "cjson.safe"
local config = require "core.config"

local _M = {
    _VERSION = '0.1.0'
}

local log = logger.new("url")

-- 预定义常量
local DEFAULT_SCHEME = "https"
local DEFAULT_PORT = {
    http = 80,
    https = 443,
    ws = 80,
    wss = 443
}

-- 添加URL验证函数
local function is_valid_url(url)
    if not url or type(url) ~= "string" then
        log.error("URL is nil or not a string")
        return false
    end

    -- 使用更通用的正则表达式检查协议和域名，支持更多协议类型
    local scheme, domain = string.match(url, "^([%w%+%.%-]+)://([^%s/]+)")

    if not scheme or not domain or domain == "" then
        log.error("Invalid URL format (missing protocol or domain): %s", url)
        return false
    end

    -- 验证协议是否支持
    local supported_protocols = {
        ["http"] = true,
        ["https"] = true,
        ["ws"] = true,
        ["wss"] = true,
        ["ftp"] = true,
        ["rtmp"] = true,
        ["rtsp"] = true
    }

    scheme = scheme:lower()
    if not supported_protocols[scheme] then
        log.warn("Protocol may not be fully supported: %s", scheme)
        -- 不返回失败，只记录警告，允许继续处理
    end

    -- 简化域名验证逻辑：只要域名包含点号且不以点号开头或结尾即可
    -- 移除可能的端口号后再检查
    local clean_domain = string.gsub(domain, ":%d+$", "")

    if not (string.find(clean_domain, "%.") and
            not string.match(clean_domain, "^%.") and
            not string.match(clean_domain, "%.$") and
            not string.find(clean_domain, "%s")) then
        log.error("Domain is invalid: %s", domain)
        return false
    end

    return true
end

-- WebSocket 协议映射修正
local WS_PROTOCOL_MAP = {
    ws = "ws",
    wss = "wss"
}

-- 优化 URL scheme 规范化
local function normalize_scheme(scheme)
    if not scheme then return DEFAULT_SCHEME end
    scheme = scheme:lower()
    return WS_PROTOCOL_MAP[scheme] or scheme
end

-- 对危险字符进行编码而非移除
local function sanitize_control_chars(str)
    if not str then return "" end

    -- 先创建本地变量避免nil赋值
    local result = str
    local new_str
    -- local _ -- 声明局部变量 _ 避免全局变量竞态条件警告

    -- 安全替换，处理可能的nil返回
    new_str, _ = ngx.re.gsub(result, "\\r", "%0D", "jo")
    if new_str then result = new_str end

    new_str, _ = ngx.re.gsub(result, "\\n", "%0A", "jo")
    if new_str then result = new_str end

    new_str, _ = ngx.re.gsub(result, "\\0", "%00", "jo")
    if new_str then result = new_str end

    return result
end

-- 使用 net_url 优化 URL 创建和解析
local function create_url(url_str)
    if not url_str then
        log.error("URL is nil")
        return nil, "Please provide a valid URL"
    end

    -- 确保 URL 有协议
    if not string.find(url_str, "^%w+://") then
        url_str = DEFAULT_SCHEME .. "://" .. url_str
        log.debug("Added default protocol: %s", url_str)
    end

    -- 验证URL格式有效性
    if not is_valid_url(url_str) then
        log.error("URL format is invalid: %s", url_str)
        return nil, "The URL format is invalid or incomplete"
    end

    local uri = net_url.parse(url_str)
    if not uri then
        log.error("Failed to parse URL: %s", url_str)
        return nil, "The URL format is invalid"
    end

    -- 规范化 scheme
    uri.scheme = normalize_scheme(uri.scheme)

    -- 规范化端口 (net_url 已经会设置默认端口，但我们保持与原有行为一致)
    if not uri.port then
        uri.port = DEFAULT_PORT[uri.scheme] or 80
    end

    -- 规范化路径
    if not uri.path or uri.path == "" then
        uri.path = "/"
    end

    return uri
end

-- 使用 net_url 优化 URL 格式化
function _M.format_target_url(url)
    local uri, err = create_url(url)
    if not uri then
        return nil, err or "Unable to process the URL"
    end

    -- 直接使用 net_url 的 build 方法
    return uri:build()
end

-- 使用原生 Lua 实现的更简单的 URL 转换函数
function _M.to_original_url(proxy_url)
    local parsed_url = net_url.parse(proxy_url)

    -- 使用cjson编码表对象以便正确打印
    if parsed_url then
        log.info("Parsed URL: %s", cjson.encode(parsed_url))
    else
        log.error("Invalid proxy URL format")
        return nil, "Invalid proxy URL format"
    end

    -- 从 proxy_url 中获取当前请求信息
    local pot_value = parsed_url.query and parsed_url.query[config.base.pot_key]
    if not pot_value then
        log.info("Missing __pot parameter in query")
        return nil, "Missing __pot parameter"
    end

    -- 解码 __pot 参数获取原始域名
    local original_origin = base64.decode_pot(pot_value)
    if not original_origin then
        log.error("Failed to decode __pot parameter: %s", pot_value)
        return nil, "Invalid destination URL format"
    end

    -- 验证解码后的URL有效性
    if not is_valid_url(original_origin) then
        log.error("Decoded URL is invalid or incomplete: %s", original_origin)
        return nil, "Invalid or incomplete destination URL"
    end

    -- 解析原始域名
    local original_origin_uri, err = _M.parse(original_origin)
    if not original_origin_uri then
        log.error("Failed to parse original host: %s, error: %s", original_origin, err)
        return nil, "Unable to process the destination URL"
    end

    -- 删除 __poxx 参数
    if parsed_url.query then
        parsed_url.query[config.base.pot_key] = nil
        parsed_url.query[config.base.service_worker_key] = nil
        parsed_url.query[config.base.web_worker_key] = nil
    end

    -- 构造 query 字符串
    local query_string = ""
    if parsed_url.query and next(parsed_url.query) then
        -- 手动构造查询字符串，不使用 buildQuery 以避免自动编码
        local query_parts = {}
        for k, v in pairs(parsed_url.query) do
            if type(v) == "table" then
                -- 如果值是表，暂时跳过复杂参数处理
                log.warn("Skipping complex query parameter: %s (table value)", k)
            else
                table.insert(query_parts, k .. "=" .. sanitize_control_chars(tostring(v)))
            end
        end

        if #query_parts > 0 then
            query_string = "?" .. table.concat(query_parts, "&")
        end
    end
    log.info("Query string: %s", query_string)

    -- 构建最终 URL
    local original_url = original_origin .. parsed_url.path .. query_string

    return original_url, original_origin_uri.host
end

-- 使用 net_url 优化代理 URL 转换
function _M.to_proxy_url(real_url, proxy_origin)
    local original_uri, err = create_url(real_url)
    if not original_uri then
        log.error("Invalid destination URL: %s", err)
        return nil, "Invalid destination URL"
    end

    local proxy_uri, err = create_url(proxy_origin)
    if not proxy_uri then
        log.error("Invalid proxy server URL: %s", err)
        return nil, "Invalid proxy server URL"
    end

    -- 修正协议转换逻辑
    local protocol = proxy_uri.scheme
    -- 只有在原始URL是WebSocket时才使用WebSocket协议
    if WS_PROTOCOL_MAP[original_uri.scheme] then
        protocol = original_uri.scheme
    end

    -- 编码原始 origin 为 pot 参数
    local origin = string.format("%s://%s", original_uri.scheme, original_uri.host)
    local pot = base64.encode_pot(origin)
    if not pot then
        log.error("Failed to encode pot parameter")
        return nil, "Failed to process the URL"
    end

    -- 创建新的 URL 对象
    local result_uri = net_url.parse(proxy_uri:build())
    if not result_uri then
        log.error("Failed to create proxy URL")
        return nil, "Failed to create proxy URL"
    end

    -- 设置路径和协议
    result_uri.path = original_uri.path
    result_uri.scheme = protocol

    -- 添加查询参数，包括 __pot
    local query = original_uri.query or {}
    query.__pot = pot
    result_uri:setQuery(query)

    -- 保留原始片段
    result_uri.fragment = original_uri.fragment

    return result_uri:build()
end

-- 使用 net_url 优化 URL 解析返回完整目标信息
function _M.parse(url_str)
    local uri, err = create_url(url_str)
    if not uri then
        log.error("Invalid URL format: %s", err)
        return nil, "Invalid URL format"
    end

    -- 返回完整的目标信息
    return {
        scheme = uri.scheme,
        host = uri.host,
        port = uri.port,
        path = uri.path,
        query = uri.query,
        full_url = uri:build()
    }
end

return _M
